from ultralytics import YOLO
import cv2
import numpy as np
import torch

# Fix for PyTorch 2.6 compatibility with YOLO models
torch.serialization.add_safe_globals([
    'ultralytics.nn.tasks.DetectionModel',
    'ultralytics.nn.modules.head.Detect',
    'ultralytics.nn.modules.conv.Conv',
    'ultralytics.nn.modules.block.C2f',
    'ultralytics.nn.modules.block.Bottleneck',
    'ultralytics.nn.modules.block.SPPF'
])

import util
from sort.sort import *
from util import get_car, read_license_plate, write_csv


results = {}

mot_tracker = Sort()

# load models
import os

# Get the current directory path
current_dir = os.path.dirname(os.path.abspath(__file__))

# Load models with absolute paths
coco_model_path = os.path.join(current_dir, 'yolov8n.pt')
license_plate_model_path = os.path.join(current_dir, 'license_plate_detector.pt')

print(f"Loading COCO model from: {coco_model_path}")
print(f"Loading license plate model from: {license_plate_model_path}")

# Check if model files exist
if not os.path.exists(coco_model_path):
    raise FileNotFoundError(f"COCO model not found at: {coco_model_path}")
if not os.path.exists(license_plate_model_path):
    raise FileNotFoundError(f"License plate model not found at: {license_plate_model_path}")

# Load models with PyTorch 2.6 compatibility
try:
    coco_model = YOLO(coco_model_path)
    license_plate_detector = YOLO(license_plate_model_path)
except Exception as e:
    print(f"Error loading models with safe globals: {e}")
    print("Trying alternative loading method...")

    # Alternative method using context manager
    with torch.serialization.safe_globals([
        'ultralytics.nn.tasks.DetectionModel',
        'ultralytics.nn.modules.head.Detect',
        'ultralytics.nn.modules.conv.Conv',
        'ultralytics.nn.modules.block.C2f',
        'ultralytics.nn.modules.block.Bottleneck',
        'ultralytics.nn.modules.block.SPPF'
    ]):
        coco_model = YOLO(coco_model_path)
        license_plate_detector = YOLO(license_plate_model_path)

print("Models loaded successfully!")

# load video
cap = cv2.VideoCapture("rtsp://livestream:stream2025!!@101.255.105.194:55411/cam/realmonitor?channel=1&subtype=0")

vehicles = [2, 3, 5, 7]

# read frames
frame_nmr = -1
ret = True
while ret:
    frame_nmr += 1
    ret, frame = cap.read()
    if ret:
        results[frame_nmr] = {}
        # detect vehicles
        detections = coco_model(frame)[0]
        detections_ = []
        for detection in detections.boxes.data.tolist():
            x1, y1, x2, y2, score, class_id = detection
            if int(class_id) in vehicles:
                detections_.append([x1, y1, x2, y2, score])

        # track vehicles
        if len(detections_) > 0:
            track_ids = mot_tracker.update(np.asarray(detections_))
        else:
            track_ids = mot_tracker.update(np.empty((0, 5)))

        # detect license plates
        license_plates = license_plate_detector(frame)[0]
        for license_plate in license_plates.boxes.data.tolist():
            x1, y1, x2, y2, score, class_id = license_plate

            # assign license plate to car
            xcar1, ycar1, xcar2, ycar2, car_id = get_car(license_plate, track_ids)

            if car_id != -1:

                # crop license plate
                license_plate_crop = frame[int(y1):int(y2), int(x1): int(x2), :]

                # process license plate
                license_plate_crop_gray = cv2.cvtColor(license_plate_crop, cv2.COLOR_BGR2GRAY)
                _, license_plate_crop_thresh = cv2.threshold(license_plate_crop_gray, 64, 255, cv2.THRESH_BINARY_INV)

                # read license plate number
                license_plate_text, license_plate_text_score = read_license_plate(license_plate_crop_thresh)

                if license_plate_text is not None:
                    results[frame_nmr][car_id] = {'car': {'bbox': [xcar1, ycar1, xcar2, ycar2]},
                                                  'license_plate': {'bbox': [x1, y1, x2, y2],
                                                                    'text': license_plate_text,
                                                                    'bbox_score': score,
                                                                    'text_score': license_plate_text_score}}

        # Create a copy of the frame for visualization
        display_frame = frame.copy()

        # Draw vehicle bounding boxes
        for track in track_ids:
            xcar1, ycar1, xcar2, ycar2, car_id = track
            cv2.rectangle(display_frame, (int(xcar1), int(ycar1)), (int(xcar2), int(ycar2)), (0, 255, 0), 2)
            cv2.putText(display_frame, f'Car ID: {int(car_id)}', (int(xcar1), int(ycar1) - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # Draw license plate bounding boxes and text
        license_plates = license_plate_detector(frame)[0]
        for license_plate in license_plates.boxes.data.tolist():
            x1, y1, x2, y2, score, class_id = license_plate
            cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 2)

            # Check if we have text for this license plate
            xcar1, ycar1, xcar2, ycar2, car_id = get_car(license_plate, track_ids)
            if car_id != -1 and car_id in results[frame_nmr]:
                if 'license_plate' in results[frame_nmr][car_id]:
                    license_text = results[frame_nmr][car_id]['license_plate']['text']
                    cv2.putText(display_frame, license_text, (int(x1), int(y1) - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

        # Display the frame
        cv2.imshow('License Plate Recognition', display_frame)

        # Break on 'q' key press
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

# cleanup
cap.release()
cv2.destroyAllWindows()

# write results
write_csv(results, './test.csv')
print("Results saved to test.csv")